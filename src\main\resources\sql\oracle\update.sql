-- 新增电子病历评级文档导出
INSERT INTO sys_config
(module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('3', 'hospital.code', '医院代码', '医院代码', '452183688', '1', 1, '0', '52', sysdate, '52', NULL, 'DQM', 'Y', '系统');
INSERT INTO sys_config
( module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('3', 'hospital.name', '医院名称', '医院名称', '南充市中心医院', '1', 1, '0', '52', sysdate, '52', NULL, 'DQM', 'Y', '系统');

ALTER TABLE MIP.DQM_DATAQUALITY_TASK_INSTANCE ADD DATA_TOTAL_NUM NUMBER (38, 0) NULL;
COMMENT ON COLUMN MIP.DQM_DATAQUALITY_TASK_INSTANCE.DATA_TOTAL_NUM IS '数据总记录数量';

ALTER TABLE MIP.DQM_DATAQUALITY_TASK_INSTANCE ADD DATA_QSTN_NUM NUMBER (38, 0) NULL;
COMMENT ON COLUMN MIP.DQM_DATAQUALITY_TASK_INSTANCE.DATA_QSTN_NUM IS '数据质量问题数量';

ALTER TABLE MIP.DQM_DATAQUALITY_TASK_INSTANCE ADD DATA_QLTY_QSTN_ID NUMBER (38, 0) NULL;
COMMENT ON COLUMN MIP.DQM_DATAQUALITY_TASK_INSTANCE.DATA_QLTY_QSTN_ID IS '问题ID';
