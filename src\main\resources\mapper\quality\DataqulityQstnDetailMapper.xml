<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.DataqulityQstnDetailMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.quality.entity.DataqulityQstnDetail">
        <!--@mbg.generated-->
        <!--@Table DQM_DATAQULITY_QSTN_DETAIL-->
        <id column="DETAIL_ID" jdbcType="INTEGER" property="detailId"/>
        <result column="DATA_QLTY_QSTN_ID" jdbcType="VARCHAR" property="dataQltyQstnId"/>
        <result column="CHECK_RULE_ID" jdbcType="VARCHAR" property="checkRuleId"/>
        <result column="DETAIL_LINE" jdbcType="VARCHAR" property="detailLine"/>
        <result column="RESERVED_FIELD1" jdbcType="VARCHAR" property="reservedField1"/>
        <result column="RESERVED_FIELD2" jdbcType="VARCHAR" property="reservedField2"/>
        <result column="RESERVED_FIELD3" jdbcType="VARCHAR" property="reservedField3"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        DETAIL_ID,
        DATA_QLTY_QSTN_ID,
        CHECK_RULE_ID,
        DETAIL_LINE,
        RESERVED_FIELD1,
        RESERVED_FIELD2,
        RESERVED_FIELD3
    </sql>
    <insert id="insertSelective" keyColumn="DETAIL_ID" keyProperty="detailId"
            parameterType="com.jykj.dqm.quality.entity.DataqulityQstnDetail" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into DQM_DATAQULITY_QSTN_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataQltyQstnId != null">
                DATA_QLTY_QSTN_ID,
            </if>
            <if test="checkRuleId != null">
                CHECK_RULE_ID,
            </if>
            <if test="detailLine != null">
                DETAIL_LINE,
            </if>
            <if test="reservedField1 != null">
                RESERVED_FIELD1,
            </if>
            <if test="reservedField2 != null">
                RESERVED_FIELD2,
            </if>
            <if test="reservedField3 != null">
                RESERVED_FIELD3,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataQltyQstnId != null">
                #{dataQltyQstnId,jdbcType=VARCHAR},
            </if>
            <if test="checkRuleId != null">
                #{checkRuleId,jdbcType=VARCHAR},
            </if>
            <if test="detailLine != null">
                #{detailLine,jdbcType=VARCHAR},
            </if>
            <if test="reservedField1 != null">
                #{reservedField1,jdbcType=VARCHAR},
            </if>
            <if test="reservedField2 != null">
                #{reservedField2,jdbcType=VARCHAR},
            </if>
            <if test="reservedField3 != null">
                #{reservedField3,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 获取指定规则ID的去重DETAIL_LINE数量，兼容Oracle CLOB字段 -->
    <select id="countDistinctDetailLine" resultType="java.lang.Long">
        <if test="_databaseId == 'oracle'">
            SELECT COUNT(DISTINCT DBMS_LOB.SUBSTR(DETAIL_LINE, 4000, 1))
            FROM DQM_DATAQULITY_QSTN_DETAIL
            WHERE CHECK_RULE_ID = #{checkRuleId}
        </if>
        <if test="_databaseId == 'mysql'">
            SELECT COUNT(DISTINCT DETAIL_LINE)
            FROM DQM_DATAQULITY_QSTN_DETAIL
            WHERE CHECK_RULE_ID = #{checkRuleId}
        </if>
    </select>
</mapper>