# YLSYB-DQM

数据质量管理（数据质量监控治理平台）后端代码

## 项目概述
本项目是一个数据质量管理系统，用于监控和治理数据质量问题。系统支持多种数据库类型，包括MySQL和Oracle。

## 主要功能
- 数据质量规则检查
- 问题数据监控和记录
- 数据质量报告生成
- 多数据源支持

## 技术栈
- Spring Boot
- MyBatis Plus
- MySQL/Oracle数据库
- Maven

## 最近修复的问题

### Oracle CLOB字段DISTINCT查询问题修复 (2025-09-03)
**问题描述：**
在Oracle数据库环境下，执行数据质量检查时出现错误：
```
ORA-00932: 数据类型不一致: 应为 -, 但却获得 CLOB
```

**问题原因：**
- `DQM_DATAQULITY_QSTN_DETAIL`表中的`DETAIL_LINE`字段在Oracle中是CLOB类型
- 代码中使用了`SELECT COUNT(DISTINCT DETAIL_LINE)`查询
- Oracle不支持对CLOB字段直接使用DISTINCT操作

**解决方案（提供多种选择）：**

**方案1：Java层去重（当前实施）**
- 移除对CLOB字段的DISTINCT查询，改为直接统计总数
- 在布隆过滤器和内存去重逻辑中，改为在Java代码层面进行去重处理
- 优点：稳定可靠，完全兼容
- 缺点：大数据量时内存消耗较大

**方案2：数据库层DBMS_LOB.SUBSTR（备选）**
- 使用Oracle的`DBMS_LOB.SUBSTR`函数截取CLOB前4000字符进行DISTINCT
- 优点：性能更好，在数据库层面解决
- 缺点：只能处理前4000字符

**方案3：自定义Mapper方法（推荐）**
- 使用MyBatis的databaseId特性自动适配不同数据库
- 结合了方案1和方案2的优点
- 代码清晰，性能优秀

详细的方案对比请参考：`Oracle_CLOB_Solutions.md`

**修改文件：**
- `src/main/java/com/jykj/dqm/quality/manager/ExecRuleCheckTask.java`
- `src/main/java/com/jykj/dqm/quality/dao/DataqulityQstnDetailMapper.java`
- `src/main/resources/mapper/quality/DataqulityQstnDetailMapper.xml`
- `Oracle_CLOB_Solutions.md`（方案对比文档）

**影响范围：**
- 数据质量检查任务执行
- 问题数据明细记录处理
- 布隆过滤器优化逻辑