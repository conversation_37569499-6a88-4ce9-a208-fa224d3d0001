# Oracle CLOB字段DISTINCT查询问题解决方案对比

## 问题背景
在Oracle数据库中，`DQM_DATAQULITY_QSTN_DETAIL`表的`DETAIL_LINE`字段是CLOB类型，使用`SELECT COUNT(DISTINCT DETAIL_LINE)`查询时会出现"ORA-00932: 数据类型不一致"错误。

## 解决方案对比

### 方案1：Java层去重（已实施）
**实现方式：**
```java
// 移除DISTINCT查询，改为直接统计总数
long count = dataqulityQstnDetailServiceImpl.count(wrapper);

// 在Java代码中进行去重
List<DataqulityQstnDetail> dataqulityQstnDetails = dataqulityQstnDetailServiceImpl.list(detailWrapper);
detailLines = dataqulityQstnDetails.stream()
    .map(DataqulityQstnDetail::getDetailLine)
    .filter(Objects::nonNull)
    .collect(Collectors.toSet());
```

**优点：**
- 保持原有业务逻辑不变
- 完全兼容MySQL和Oracle
- 不需要修改数据库结构
- 实现简单，风险低

**缺点：**
- 大数据量时内存消耗较大
- 需要将所有数据加载到Java内存中
- 性能相对较低

**适用场景：** 数据量中等，对内存使用不敏感的场景

---

### 方案2：使用Oracle DBMS_LOB.SUBSTR函数
**实现方式：**
```sql
-- Oracle
SELECT COUNT(DISTINCT DBMS_LOB.SUBSTR(DETAIL_LINE, 4000, 1))
FROM DQM_DATAQULITY_QSTN_DETAIL
WHERE CHECK_RULE_ID = ?

-- MySQL
SELECT COUNT(DISTINCT DETAIL_LINE)
FROM DQM_DATAQULITY_QSTN_DETAIL
WHERE CHECK_RULE_ID = ?
```

**优点：**
- 在数据库层面解决问题
- 性能较好，不需要加载大量数据到内存
- 充分利用数据库的优化能力

**缺点：**
- 只能截取CLOB的前4000个字符
- 如果DETAIL_LINE内容超过4000字符且前4000字符相同，可能出现误判
- 需要针对不同数据库编写不同的SQL

**适用场景：** DETAIL_LINE内容较短（<4000字符）且对性能要求较高的场景

---

### 方案3：自定义Mapper方法（推荐）
**实现方式：**
```java
// Mapper接口
Long countDistinctDetailLine(Integer checkRuleId);
```

```xml
<!-- XML映射文件 -->
<select id="countDistinctDetailLine" resultType="java.lang.Long">
    <if test="_databaseId == 'oracle'">
        SELECT COUNT(DISTINCT DBMS_LOB.SUBSTR(DETAIL_LINE, 4000, 1))
        FROM DQM_DATAQULITY_QSTN_DETAIL
        WHERE CHECK_RULE_ID = #{checkRuleId}
    </if>
    <if test="_databaseId == 'mysql'">
        SELECT COUNT(DISTINCT DETAIL_LINE)
        FROM DQM_DATAQULITY_QSTN_DETAIL
        WHERE CHECK_RULE_ID = #{checkRuleId}
    </if>
</select>
```

**优点：**
- 结合了方案1和方案2的优点
- 使用MyBatis的databaseId特性自动适配不同数据库
- 代码清晰，易于维护
- 性能优秀
- 符合分层架构原则

**缺点：**
- 需要修改Mapper接口和XML文件
- 仍然有4000字符截取的限制

**适用场景：** 推荐作为最佳实践，适用于大多数场景

---

### 方案4：数据库结构优化（长期方案）
**实现方式：**
```sql
-- 添加DETAIL_LINE的哈希值字段
ALTER TABLE DQM_DATAQULITY_QSTN_DETAIL ADD DETAIL_LINE_HASH VARCHAR2(64);

-- 创建索引
CREATE INDEX IDX_DETAIL_LINE_HASH ON DQM_DATAQULITY_QSTN_DETAIL(CHECK_RULE_ID, DETAIL_LINE_HASH);

-- 使用哈希值进行去重
SELECT COUNT(DISTINCT DETAIL_LINE_HASH)
FROM DQM_DATAQULITY_QSTN_DETAIL
WHERE CHECK_RULE_ID = ?
```

**优点：**
- 完美解决CLOB字段DISTINCT问题
- 性能最优
- 支持任意长度的DETAIL_LINE内容
- 可以创建索引提升查询性能

**缺点：**
- 需要修改数据库结构
- 需要修改插入逻辑，计算哈希值
- 存在极小概率的哈希冲突
- 实施复杂度较高

**适用场景：** 长期规划，对性能要求极高的场景

---

## 推荐选择

1. **当前环境（紧急修复）：** 使用方案1，已经实施，稳定可靠
2. **短期优化：** 升级到方案3，获得更好的性能
3. **长期规划：** 考虑方案4，彻底解决问题

## 实施建议

1. 保持当前的方案1作为稳定版本
2. 在测试环境验证方案3的效果
3. 根据实际数据量和性能需求选择最适合的方案
4. 如果DETAIL_LINE内容普遍较长（>4000字符），建议考虑方案4
